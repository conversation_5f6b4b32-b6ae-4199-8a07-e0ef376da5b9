# Ignore Python virtual environment
.env/

# Ignore JARs and other binaries
*.jar
*.zip

# Ignore Python cache
__pycache__/
*.pyc

# Ignore VSCode settings
.vscode/

# Ignore logs
*.log

# Ignore AWS configuration files with real credentials
# src/main/config/data/aws/aws.dev.yaml  # Allow dev config with dummy credentials
# src/main/config/data/aws/aws.emr.yaml  # Allow EMR config template

localstack/