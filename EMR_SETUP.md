# ⚙️ Configuration Changes

### 1. Key Files to Modify

| Component | Local Development | EMR Production |
|-----------|------------------|----------------|
| **Job Config** | `job.iceberg.sample.yaml` | `job.emr.yaml` |
| **AWS Environment** | `AWS_ENVIRONMENT=dev` | `AWS_ENVIRONMENT=emr` |
| **MySQL Endpoint** | `mysql.endpoint.yaml` | `mysql.prod.endpoint.yaml` |
| **SQL Server Endpoint** | `mssql.endpoint.yaml` | `mssql.prod.endpoint.yaml` |
| **S3 Endpoint** | `s3.localstack.endpoint.yaml` | `s3.prod.endpoint.yaml` |

### 2. Environment Variables

```bash
# AWS Configuration
export AWS_ENVIRONMENT=emr
export AWS_DEFAULT_REGION=us-east-1

# Database Configuration
export MYSQL_HOST=your-mysql-host.amazonaws.com
export MYSQL_USERNAME=your-username
export MYSQL_PASSWORD=your-password
export MYSQL_DATABASE=your-database
export MYSQL_SCHEMA=your-schema

export MSSQL_HOST=your-sqlserver-host.amazonaws.com
export MSSQL_USERNAME=your-username
export MSSQL_PASSWORD=your-password
export MSSQL_DATABASE=your-database
export MSSQL_SCHEMA=dbo

# S3 Configuration
export S3_BUCKET_NAME=your-s3-bucket-name
```

### 3. Pipeline Command for EMR

```bash
# EMR command (remove LocalStack-specific parameters)
python3 src/main/pipeline_main.py \
  --config_file job.emr.yaml \
  --config_source_type s3 \
  --config_root_dir src/main/config \
  --app_type batch
```
